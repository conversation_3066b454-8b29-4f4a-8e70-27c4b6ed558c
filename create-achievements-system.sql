-- Create achievements system tables
-- Run this in Supabase SQL Editor

-- Create achievements table to define all available achievements
CREATE TABLE IF NOT EXISTS public.achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    icon_name TEXT NOT NULL, -- Lucide icon name
    category TEXT NOT NULL CHECK (category IN ('distance', 'streak', 'frequency', 'milestone', 'special')),
    tier TEXT CHECK (tier IN ('Bronze', 'Silver', 'Gold', 'Platinum', 'Diamond')),
    requirement_type TEXT NOT NULL CHECK (requirement_type IN ('distance_single', 'distance_total', 'streak_days', 'activities_count', 'special')),
    requirement_value NUMERIC(10, 2) NOT NULL,
    points INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create user_achievements table to track user progress
CREATE TABLE IF NOT EXISTS public.user_achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    achievement_id UUID NOT NULL REFERENCES public.achievements(id) ON DELETE CASCADE,
    unlocked_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    progress_value NUMERIC(10, 2) DEFAULT 0,
    UNIQUE(user_id, achievement_id)
);

-- Enable RLS
ALTER TABLE public.achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;

-- RLS Policies for achievements (public read)
CREATE POLICY "Achievements are viewable by everyone" 
ON public.achievements FOR SELECT 
USING (true);

-- RLS Policies for user_achievements
CREATE POLICY "Users can view their own achievements" 
ON public.user_achievements FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own achievements" 
ON public.user_achievements FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all user achievements" 
ON public.user_achievements FOR SELECT 
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'admin'
  )
);

-- Insert default achievements
INSERT INTO public.achievements (name, title, description, icon_name, category, tier, requirement_type, requirement_value, points) VALUES
-- Distance achievements
('first_km', 'First Steps', 'Complete your first 1km activity', 'Footprints', 'distance', 'Bronze', 'distance_single', 1.0, 10),
('daily_5k', 'Daily 5K', 'Complete a 5km activity in a single day', 'Target', 'distance', 'Silver', 'distance_single', 5.0, 25),
('daily_10k', 'Daily 10K', 'Complete a 10km activity in a single day', 'Trophy', 'distance', 'Gold', 'distance_single', 10.0, 50),
('daily_21k', 'Half Marathon', 'Complete a 21km activity in a single day', 'Medal', 'distance', 'Platinum', 'distance_single', 21.0, 100),
('daily_42k', 'Marathon Master', 'Complete a 42km activity in a single day', 'Crown', 'distance', 'Diamond', 'distance_single', 42.0, 200),

-- Total distance achievements
('total_50k', 'Distance Explorer', 'Reach 50km total distance', 'Map', 'milestone', 'Bronze', 'distance_total', 50.0, 30),
('total_100k', 'Century Runner', 'Reach 100km total distance', 'MapPin', 'milestone', 'Silver', 'distance_total', 100.0, 60),
('total_500k', 'Distance Warrior', 'Reach 500km total distance', 'Mountain', 'milestone', 'Gold', 'distance_total', 500.0, 150),
('total_1000k', 'Kilometer King', 'Reach 1000km total distance', 'Zap', 'milestone', 'Platinum', 'distance_total', 1000.0, 300),

-- Streak achievements
('streak_3', 'Getting Started', 'Maintain a 3-day activity streak', 'Flame', 'streak', 'Bronze', 'streak_days', 3, 15),
('streak_7', 'Week Warrior', 'Maintain a 7-day activity streak', 'Fire', 'streak', 'Silver', 'streak_days', 7, 35),
('streak_30', 'Monthly Master', 'Maintain a 30-day activity streak', 'Sparkles', 'streak', 'Gold', 'streak_days', 30, 100),
('streak_100', 'Streak Legend', 'Maintain a 100-day activity streak', 'Star', 'streak', 'Platinum', 'streak_days', 100, 250),

-- Frequency achievements
('activities_10', 'Active Starter', 'Complete 10 activities', 'Activity', 'frequency', 'Bronze', 'activities_count', 10, 20),
('activities_50', 'Consistent Performer', 'Complete 50 activities', 'BarChart3', 'frequency', 'Silver', 'activities_count', 50, 75),
('activities_100', 'Dedication Master', 'Complete 100 activities', 'TrendingUp', 'frequency', 'Gold', 'activities_count', 100, 150),
('activities_365', 'Year Round Athlete', 'Complete 365 activities', 'Calendar', 'frequency', 'Platinum', 'activities_count', 365, 400),

-- Special achievements
('early_bird', 'Early Bird', 'Complete an activity before 6 AM', 'Sunrise', 'special', 'Silver', 'special', 1, 25),
('night_owl', 'Night Owl', 'Complete an activity after 10 PM', 'Moon', 'special', 'Silver', 'special', 1, 25),
('weekend_warrior', 'Weekend Warrior', 'Complete activities on both Saturday and Sunday', 'Calendar', 'special', 'Bronze', 'special', 1, 20)

ON CONFLICT (name) DO NOTHING;

-- Function to check and award achievements
CREATE OR REPLACE FUNCTION public.check_and_award_achievements(p_user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    achievement_record RECORD;
    user_total_distance NUMERIC;
    user_activity_count INTEGER;
    user_current_streak INTEGER;
    latest_activity RECORD;
BEGIN
    -- Get user stats
    SELECT COALESCE(SUM(distance_km), 0) INTO user_total_distance
    FROM public.user_activities 
    WHERE user_id = p_user_id;
    
    SELECT COUNT(*) INTO user_activity_count
    FROM public.user_activities 
    WHERE user_id = p_user_id;
    
    -- Get latest activity for single distance checks
    SELECT distance_km, activity_date INTO latest_activity
    FROM public.user_activities 
    WHERE user_id = p_user_id 
    ORDER BY activity_date DESC, created_at DESC 
    LIMIT 1;
    
    -- Calculate current streak (simplified)
    SELECT COUNT(*) INTO user_current_streak
    FROM (
        SELECT activity_date
        FROM public.user_activities 
        WHERE user_id = p_user_id 
        AND activity_date >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY activity_date
        ORDER BY activity_date DESC
    ) daily_activities;
    
    -- Check each achievement
    FOR achievement_record IN 
        SELECT * FROM public.achievements WHERE is_active = true
    LOOP
        -- Skip if user already has this achievement
        IF EXISTS (
            SELECT 1 FROM public.user_achievements 
            WHERE user_id = p_user_id AND achievement_id = achievement_record.id
        ) THEN
            CONTINUE;
        END IF;
        
        -- Check achievement requirements
        CASE achievement_record.requirement_type
            WHEN 'distance_single' THEN
                IF latest_activity.distance_km >= achievement_record.requirement_value THEN
                    INSERT INTO public.user_achievements (user_id, achievement_id, progress_value)
                    VALUES (p_user_id, achievement_record.id, latest_activity.distance_km);
                END IF;
                
            WHEN 'distance_total' THEN
                IF user_total_distance >= achievement_record.requirement_value THEN
                    INSERT INTO public.user_achievements (user_id, achievement_id, progress_value)
                    VALUES (p_user_id, achievement_record.id, user_total_distance);
                END IF;
                
            WHEN 'activities_count' THEN
                IF user_activity_count >= achievement_record.requirement_value THEN
                    INSERT INTO public.user_achievements (user_id, achievement_id, progress_value)
                    VALUES (p_user_id, achievement_record.id, user_activity_count);
                END IF;
                
            WHEN 'streak_days' THEN
                IF user_current_streak >= achievement_record.requirement_value THEN
                    INSERT INTO public.user_achievements (user_id, achievement_id, progress_value)
                    VALUES (p_user_id, achievement_record.id, user_current_streak);
                END IF;
        END CASE;
    END LOOP;
END;
$$;

-- Trigger to check achievements when activities are added
CREATE OR REPLACE FUNCTION public.trigger_check_achievements()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
    PERFORM public.check_and_award_achievements(NEW.user_id);
    RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS on_activity_check_achievements ON public.user_activities;
CREATE TRIGGER on_activity_check_achievements
AFTER INSERT ON public.user_activities
FOR EACH ROW
EXECUTE FUNCTION public.trigger_check_achievements();
