-- Create guild chat system
-- Run this in Supabase SQL Editor

-- Create guild_messages table for guild-specific chat
CREATE TABLE IF NOT EXISTS public.guild_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    guild_id UUID NOT NULL REFERENCES public.guilds(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    message_type TEXT NOT NULL DEFAULT 'text' CHECK (message_type IN ('text', 'system', 'achievement')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    is_edited BOOLEAN NOT NULL DEFAULT false
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_guild_messages_guild_id ON public.guild_messages(guild_id);
CREATE INDEX IF NOT EXISTS idx_guild_messages_created_at ON public.guild_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_guild_messages_user_id ON public.guild_messages(user_id);

-- Enable Row Level Security
ALTER TABLE public.guild_messages ENABLE ROW LEVEL SECURITY;

-- RLS Policies for guild messages
-- Users can only see messages from guilds they are members of
CREATE POLICY "Guild members can view guild messages" 
ON public.guild_messages FOR SELECT 
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.guild_members 
        WHERE guild_members.guild_id = guild_messages.guild_id 
        AND guild_members.user_id = auth.uid()
    )
);

-- Users can only insert messages to guilds they are members of
CREATE POLICY "Guild members can insert guild messages" 
ON public.guild_messages FOR INSERT 
TO authenticated
WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
        SELECT 1 FROM public.guild_members 
        WHERE guild_members.guild_id = guild_messages.guild_id 
        AND guild_members.user_id = auth.uid()
    )
);

-- Users can only update their own messages
CREATE POLICY "Users can update their own guild messages" 
ON public.guild_messages FOR UPDATE 
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Users can only delete their own messages (or guild leaders can delete any)
CREATE POLICY "Users can delete their own guild messages or leaders can delete any" 
ON public.guild_messages FOR DELETE 
TO authenticated
USING (
    auth.uid() = user_id OR
    EXISTS (
        SELECT 1 FROM public.guild_members 
        WHERE guild_members.guild_id = guild_messages.guild_id 
        AND guild_members.user_id = auth.uid()
        AND guild_members.role = 'leader'
    )
);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_guild_message_updated_at()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = now();
    NEW.is_edited = true;
    RETURN NEW;
END;
$$;

-- Trigger to update updated_at on message edits
DROP TRIGGER IF EXISTS on_guild_message_updated ON public.guild_messages;
CREATE TRIGGER on_guild_message_updated
BEFORE UPDATE ON public.guild_messages
FOR EACH ROW
EXECUTE FUNCTION public.update_guild_message_updated_at();

-- Function to send system messages
CREATE OR REPLACE FUNCTION public.send_guild_system_message(
    p_guild_id UUID,
    p_content TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    message_id UUID;
BEGIN
    INSERT INTO public.guild_messages (guild_id, user_id, content, message_type)
    VALUES (p_guild_id, '00000000-0000-0000-0000-000000000000', p_content, 'system')
    RETURNING id INTO message_id;
    
    RETURN message_id;
END;
$$;

-- Function to send achievement messages
CREATE OR REPLACE FUNCTION public.send_guild_achievement_message(
    p_guild_id UUID,
    p_user_id UUID,
    p_achievement_name TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    message_id UUID;
    username TEXT;
BEGIN
    -- Get username
    SELECT profiles.username INTO username
    FROM public.profiles
    WHERE profiles.id = p_user_id;
    
    INSERT INTO public.guild_messages (guild_id, user_id, content, message_type)
    VALUES (
        p_guild_id, 
        p_user_id, 
        username || ' unlocked the "' || p_achievement_name || '" achievement! 🏆', 
        'achievement'
    )
    RETURNING id INTO message_id;
    
    RETURN message_id;
END;
$$;

-- Trigger to send system messages when users join/leave guilds
CREATE OR REPLACE FUNCTION public.guild_member_change_notification()
RETURNS trigger
LANGUAGE plpgsql
AS $$
DECLARE
    username TEXT;
BEGIN
    -- Get username
    SELECT profiles.username INTO username
    FROM public.profiles
    WHERE profiles.id = COALESCE(NEW.user_id, OLD.user_id);
    
    IF TG_OP = 'INSERT' THEN
        -- User joined guild
        PERFORM public.send_guild_system_message(
            NEW.guild_id,
            username || ' joined the guild! Welcome! 👋'
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- User left guild
        PERFORM public.send_guild_system_message(
            OLD.guild_id,
            username || ' left the guild. 👋'
        );
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$;

DROP TRIGGER IF EXISTS on_guild_member_change ON public.guild_members;
CREATE TRIGGER on_guild_member_change
AFTER INSERT OR DELETE ON public.guild_members
FOR EACH ROW
EXECUTE FUNCTION public.guild_member_change_notification();

-- Set up realtime for guild messages
ALTER TABLE public.guild_messages REPLICA IDENTITY FULL;
ALTER PUBLICATION supabase_realtime ADD TABLE public.guild_messages;

-- Update the existing messages table to add a channel_type for global vs guild
ALTER TABLE public.messages 
ADD COLUMN IF NOT EXISTS channel_type TEXT NOT NULL DEFAULT 'global' CHECK (channel_type IN ('global', 'guild'));

-- Add guild_id to messages table for future guild channel support
ALTER TABLE public.messages 
ADD COLUMN IF NOT EXISTS guild_id UUID REFERENCES public.guilds(id) ON DELETE SET NULL;

-- Update RLS policies for messages to handle guild channels
DROP POLICY IF EXISTS "Public_messages_are_viewable_by_everyone" ON public.messages;
DROP POLICY IF EXISTS "Users_can_insert_their_own_messages" ON public.messages;

CREATE POLICY "Global messages are viewable by everyone" 
ON public.messages FOR SELECT 
USING (channel_type = 'global');

CREATE POLICY "Guild messages are viewable by guild members" 
ON public.messages FOR SELECT 
USING (
    channel_type = 'guild' AND
    EXISTS (
        SELECT 1 FROM public.guild_members 
        WHERE guild_members.guild_id = messages.guild_id 
        AND guild_members.user_id = auth.uid()
    )
);

CREATE POLICY "Users can insert global messages" 
ON public.messages FOR INSERT 
WITH CHECK (auth.uid() = user_id AND channel_type = 'global');

CREATE POLICY "Guild members can insert guild messages" 
ON public.messages FOR INSERT 
WITH CHECK (
    auth.uid() = user_id AND 
    channel_type = 'guild' AND
    EXISTS (
        SELECT 1 FROM public.guild_members 
        WHERE guild_members.guild_id = messages.guild_id 
        AND guild_members.user_id = auth.uid()
    )
);
